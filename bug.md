PS D:\project\ai-dance\legend_dance> flutter run -d FMR0224924017063
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on ALN AL00 in debug mode...
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
lib/pages/home/<USER>/home_controller.dart:190:13: Error: No named parameter with the name 'vipExpireTime'.
            vipExpireTime: userInfo.value?.vipExpireTime,
            ^^^^^^^^^^^^^
lib/utils/analytics/base_analytics.dart:51:23: Context: Found this candidate, but the arguments don't match.
  static Future<void> updateUserInfo({
                      ^^^^^^^^^^^^^^
lib/pages/home/<USER>/home_controller.dart:226:56: Error: The argument type 'DateTime' can't be assigned to the parameter type 'String?'.
 - 'DateTime' is from 'dart:core'.
        await VipCacheService.inferVipLevelFromEndTime(vipExpireTime, userId);
                                                       ^
lib/pages/home/<USER>/home_controller.dart:249:69: Error: Too many positional arguments: 1 allowed, but 2 found.
Try removing the extra positional arguments.
        final response = await _apiClient.post<Map<String, dynamic>>(
                                                                    ^
lib/pages/home/<USER>/home_controller.dart:291:75: Error: The getter 'name' isn't defined for the class 'DanceTypeChild'.
 - 'DanceTypeChild' is from 'package:keepdance/models/dance_type.dart' ('lib/models/dance_type.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'name'.
        recommendChildren.add(TypeItemDataChild(id: child.id, name: child.name));
                                                                          ^^^^
lib/pages/home/<USER>/home_controller.dart:294:76: Error: The getter 'name' isn't defined for the class 'DanceType'.
 - 'DanceType' is from 'package:keepdance/models/dance_type.dart' ('lib/models/dance_type.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'name'.
    newTypeList.add(TypeItemData(id: recommendType.id, name: recommendType.name, children: recommendChildren));
                                                                           ^^^^
lib/pages/home/<USER>/home_controller.dart:299:70: Error: The getter 'name' isn't defined for the class 'DanceType'.
 - 'DanceType' is from 'package:keepdance/models/dance_type.dart' ('lib/models/dance_type.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'name'.
        newTypeList.add(TypeItemData(id: typeData.id, name: typeData.name, children: <TypeItemDataChild>[].obs));
                                                                     ^^^^
lib/pages/home/<USER>/home_controller.dart:309:63: Error: Too many positional arguments: 0 allowed, but 1 found.
Try removing the extra positional arguments.
      final themes = await _danceThemeService.fetchDanceThemes(headers);
                                                              ^
lib/pages/home/<USER>/home_controller.dart:351:76: Error: Too few positional arguments: 2 required, 0 given.
        final newHistory = await _danceHistoryService.fetchUserDanceHistory(
                                                                           ^
lib/pages/home/<USER>/home_controller.dart:417:59: Error: The method 'fetchJustDanceMaterialList' isn't defined for the class 'JustDanceCategoryService'.
 - 'JustDanceCategoryService' is from 'package:keepdance/pages/home/<USER>/just_dance_category_service.dart' ('lib/pages/home/<USER>/just_dance_category_service.dart').
Try correcting the name to the name of an existing method, or defining a method named 'fetchJustDanceMaterialList'.
        final materials = await _justDanceCategoryService.fetchJustDanceMaterialList(
                                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/home/<USER>/home_controller.dart:494:50: Error: Too many positional arguments: 3 allowed, but 4 found.
Try removing the extra positional arguments.
    await _errorClassificationService.handleError(
                                                 ^
lib/pages/creation/views/widgets/creation/work_item_wrapper.dart:58:61: Error: Too many positional arguments: 0 allowed, but 1 found.
Try removing the extra positional arguments.
        onTap: onTap != null ? (WorkItemData data) => onTap!(data) : null,
                                                            ^
lib/pages/creation/services/vip_quota_service.dart:39:28: Error: The getter 'vipInfo' isn't defined for the class 'User'.
 - 'User' is from 'package:keepdance/models/user.dart' ('lib/models/user.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'vipInfo'.
      final vipInfo = user.vipInfo;
                           ^^^^^^^
lib/pages/creation/services/dance_share_service.dart:157:38: Error: Method not found: 'isWeChatInstalled'.
      bool isInstalled = await fluwx.isWeChatInstalled();
                                     ^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:205:50: Error: Undefined name 'WeChatImage'.
        thumbnail: thumbnailData != null ? fluwx.WeChatImage.binary(thumbnailData) : null,
                                                 ^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:200:68: Error: Too few positional arguments: 1 required, 0 given.
      fluwx.WeChatShareFileModel model = fluwx.WeChatShareFileModel(
                                                                   ^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fluwx-5.7.2/lib/src/foundation/share_models.dart:309:3: Context: Found this candidate, but the arguments don't match.
  WeChatShareFileModel(
  ^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:211:26: Error: Undefined name 'weChatResponseEventHandler'.
      subscriber = fluwx.weChatResponseEventHandler.distinct().listen((fluwx.WeChatShareResponse response) {
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:221:19: Error: Method not found: 'shareToWeChat'.
      await fluwx.shareToWeChat(model);
                  ^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:383:19: Error: Method not found: 'registerWxApi'.
      await fluwx.registerWxApi(
                  ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:563:34: Error: Member not found: 'instance'.
      await PoseCameraController.instance.dispose();
                                 ^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:577:45: Error: A value of type 'RxList<dynamic>' can't be assigned to a variable of type 'List<String>'.
 - 'RxList' is from 'package:get/get_rx/src/rx_types/rx_types.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib/get_rx/src/rx_types/rx_types.dart').
 - 'List' is from 'dart:core'.
    GlobalData.instance.localVideoPath = [].obs;
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:676:13: Error: No named parameter with the name 'useAsmsVideoTracks'.
            useAsmsVideoTracks: true,
            ^^^^^^^^^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/configuration/better_player_data_source.dart:79:3: Context: Found this candidate, but the arguments don't match.
  BetterPlayerDataSource(
  ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:747:32: Error: This expression has type 'void' and can't be used.
                 _speedService.onInit().catchError((e, s) {
                               ^
lib/pages/video_detail/controllers/video_detail_controller.dart:747:41: Error: The method 'catchError' isn't defined for the class 'void'.
Try correcting the name to the name of an existing method, or defining a method named 'catchError'.
                 _speedService.onInit().catchError((e, s) {
                                        ^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:774:49: Error: The method '_showVipUpgradeDialog' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named '_showVipUpgradeDialog'.
                (this as VideoDetailController)._showVipUpgradeDialog(speed);
                                                ^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:781:55: Error: The method '_applySpeedToPlayerDirectly' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named '_applySpeedToPlayerDirectly'.
                await (this as VideoDetailController)._applySpeedToPlayerDirectly(speed);
                                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:782:49: Error: The method 'onSpeedChanged' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'onSpeedChanged'.
                (this as VideoDetailController).onSpeedChanged(speed);
                                                ^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:860:33: Error: A value of type 'RxBool' can't be assigned to a variable of type 'bool'.
 - 'RxBool' is from 'package:get/get_rx/src/rx_types/rx_types.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib/get_rx/src/rx_types/rx_types.dart').
          if (!interactionState.isInitialized) {
                                ^
lib/pages/video_detail/controllers/video_detail_controller.dart:875:36: Error: Too few positional arguments: 2 required, 0 given.
              interactionState.init(
                                   ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1056:63: Error: The argument type 'DanceVideoDetail' can't be assigned to the parameter type 'String'.
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
          await titleAnimationController.updateStatusBarColor(detail);
                                                              ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1070:49: Error: A value of type '(CommunityDetail?, DanceVideoDetail?)' can't be assigned to a variable of type 'CommunityDetail?'.
 - 'CommunityDetail' is from 'package:keepdance/models/community_detail.dart' ('lib/models/community_detail.dart').
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
        _videoInfoState.communityDetail.value = detail;
                                                ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1087:60: Error: A value of type 'Map<String, dynamic>' can't be assigned to a variable of type 'DanceScoreDetail?'.
 - 'Map' is from 'dart:core'.
 - 'DanceScoreDetail' is from 'package:keepdance/models/dance_score_detail.dart' ('lib/models/dance_score_detail.dart').
        _videoInfoState.danceScoreDetail.value = bestScore.toDanceScoreDetailJson();
                                                           ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1108:56: Error: The getter 'progressRank' isn't defined for the class 'DanceScoreDetail'.
 - 'DanceScoreDetail' is from 'package:keepdance/models/dance_score_detail.dart' ('lib/models/dance_score_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'progressRank'.
           _videoInfoState.progressRank.value = detail.progressRank ?? "0";
                                                       ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1109:56: Error: The getter 'progressRate' isn't defined for the class 'DanceScoreDetail'.
 - 'DanceScoreDetail' is from 'package:keepdance/models/dance_score_detail.dart' ('lib/models/dance_score_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'progressRate'.
           _videoInfoState.progressRate.value = detail.progressRate ?? "0.0";
                                                       ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1126:45: Error: A value of type 'String' can't be assigned to a variable of type 'int'.
      int videoId = _videoInfoState.videoId.value;
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1133:50: Error: A value of type 'RankingData' can't be assigned to a variable of type 'List<dynamic>'.
 - 'RankingData' is from 'package:keepdance/models/ranking_data.dart' ('lib/models/ranking_data.dart').
 - 'List' is from 'dart:core'.
        _videoInfoState.maxScoreSortList.value = rankingData;
                                                 ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1147:7: Error: The getter '_isLoadingLocalHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isLoadingLocalHistory'.
      _isLoadingLocalHistory.value = true;
      ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1157:15: Error: The method 'LateInitializationError' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'LateInitializationError'.
        throw LateInitializationError('_videoInfoState');
              ^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1167:7: Error: The getter '_localVideoHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoHistory'.
      _localVideoHistory.value = historyList;
      ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1170:7: Error: The getter '_localVideoStatistics' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoStatistics'.
      _localVideoStatistics.value = statistics;
      ^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1183:7: Error: The getter '_localVideoHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoHistory'.
      _localVideoHistory.clear();
      ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1184:7: Error: The getter '_localVideoStatistics' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoStatistics'.
      _localVideoStatistics.clear();
      ^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1188:7: Error: The getter '_isLoadingLocalHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isLoadingLocalHistory'.
      _isLoadingLocalHistory.value = false;
      ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1222:15: Error: The method 'LateInitializationError' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'LateInitializationError'.
        throw LateInitializationError('_videoInfoState');
              ^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1226:45: Error: A value of type 'String' can't be assigned to a variable of type 'int'.
      int videoId = _videoInfoState.videoId.value;
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1247:41: Error: A value of type 'bool' can't be assigned to a variable of type 'int'.
      _videoInfoState.vipStatus.value = status;
                                        ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1343:45: Error: Too many positional arguments: 0 allowed, but 6 found.
Try removing the extra positional arguments.
    return await _reportService.submitReport(reasonId, content, contact, images, type, targetId);
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1360:17: Error: The getter 'animationController' isn't defined for the class 'ChartAnimationUtil'.
 - 'ChartAnimationUtil' is from 'package:keepdance/pages/video_detail/utils/chart_animation_util.dart' ('lib/pages/video_detail/utils/chart_animation_util.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'animationController'.
        if(util.animationController == null) {
                ^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1365:29: Error: No named parameter with the name 'isSingle'.
        util.startAnimation(isSingle: _videoInfoState.currentTabIndex.value == 2);
                            ^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1400:62: Error: The getter 'score' isn't defined for the class 'LocalVideoScore'.
 - 'LocalVideoScore' is from 'package:keepdance/models/local_video_score.dart' ('lib/models/local_video_score.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'score'.
    double totalScore = history.map((e) => double.tryParse(e.score) ?? 0.0).reduce((a, b) => a + b);
                                                             ^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1401:61: Error: The getter 'score' isn't defined for the class 'LocalVideoScore'.
 - 'LocalVideoScore' is from 'package:keepdance/models/local_video_score.dart' ('lib/models/local_video_score.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'score'.
    double bestScore = history.map((e) => double.tryParse(e.score) ?? 0.0).reduce((a, b) => a > b ? a : b);
                                                            ^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1402:62: Error: The getter 'score' isn't defined for the class 'LocalVideoScore'.
 - 'LocalVideoScore' is from 'package:keepdance/models/local_video_score.dart' ('lib/models/local_video_score.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'score'.
    int validCount = history.where((e) => (double.tryParse(e.score) ?? 0.0) > 0).length;
                                                             ^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1451:41: Error: Too few positional arguments: 8 required, 0 given.
    EventAnalytics.trackVideoButtonClick(
                                        ^
lib/utils/analytics/event_analytics.dart:264:15: Context: Found this candidate, but the arguments don't match.
  static void trackVideoButtonClick(
              ^^^^^^^^^^^^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart:22:23: Error: The method 'hashValues' isn't defined for the class 'DrmInitData'.
 - 'DrmInitData' is from 'package:better_player/src/hls/hls_parser/drm_init_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(schemeType, schemeData);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart:52:23: Error: The method 'hashValues' isn't defined for the class 'SchemeData'.
 - 'SchemeData' is from 'package:better_player/src/hls/hls_parser/scheme_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart:31:23: Error: The method 'hashValues' isn't defined for the class 'HlsTrackMetadataEntry'.
 - 'HlsTrackMetadataEntry' is from 'package:better_player/src/hls/hls_parser/hls_track_metadata_entry.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(groupId, name, variantInfos);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart:44:23: Error: The method 'hashValues' isn't defined for the class 'VariantInfo'.
 - 'VariantInfo' is from 'package:better_player/src/hls/hls_parser/variant_info.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
lib/pages/creation/services/pagination_service.dart:269:69: Error: The argument type 'String?' can't be assigned to the parameter type 'String' because 'String?' is nullable and 'String' isn't.
      data = data.where((item) => PinyinSearchUtil.matchSearch(item.title, _searchKeyword));
                                                                    ^
lib/pages/creation/views/widgets/creation/empty_state_widget.dart:109:26: Error: The getter 'HapticFeedback' isn't defined for the class '_EmptyStateWidgetState'.
 - '_EmptyStateWidgetState' is from 'package:keepdance/pages/creation/views/widgets/creation/empty_state_widget.dart' ('lib/pages/creation/views/widgets/creation/empty_state_widget.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'HapticFeedback'.
                         HapticFeedback.mediumImpact();
                         ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/empty_state_widget.dart:169:26: Error: The getter 'HapticFeedback' isn't defined for the class '_EmptyStateWidgetState'.
 - '_EmptyStateWidgetState' is from 'package:keepdance/pages/creation/views/widgets/creation/empty_state_widget.dart' ('lib/pages/creation/views/widgets/creation/empty_state_widget.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'HapticFeedback'.
                         HapticFeedback.mediumImpact();
                         ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/empty_state_widget.dart:376:23: Error: Method not found: 'Random'.
    final random = ui.Random(153);
                      ^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:637:12: Error: The method 'hashValues' isn't defined for the class 'PaletteTarget'.
 - 'PaletteTarget' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(
           ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:856:12: Error: The method 'hashValues' isn't defined for the class 'PaletteColor'.
 - 'PaletteColor' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(color, population);
           ^^^^^^^^^^
Target kernel_snapshot_program failed: Exception


FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileFlutterBuildDebug'.
> Process 'command 'D:\Program Files\Flutterdev\flutter\bin\flutter.bat'' finished with non-zero exit value 1

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 5s
Running Gradle task 'assembleDebug'...                              6.2s
Error: Gradle task assembleDebug failed with exit code 1